#include "download_manager.h"
#include "handle_reason.h"
#include "logger/logger.h"
#include <cassert>

namespace seres
{
namespace fotamaster
{

DownloadManager::DownloadManager(DUCServiceManager *duc_service_manager)
    : duc_srv_manager_{duc_service_manager}
{
    progress_manager_ = &base::Singleton<DownloadProgressManager>::Instance();
    assert(progress_manager_);
    assert(Initialize());
}

bool DownloadManager::Initialize()
{
    //初始化进度拟合管理器
    auto ret = progress_manager_->Initialize([this](uint8_t progress,
                                                    uint64_t allPakageSize,
                                                    uint32_t speed,
                                                    uint32_t remainingTime) {
        if (progress_callback_)
            this->progress_callback_(progress);
        else
            LOG_ERROR("Download overall progress callback not registered");
    });

    if (!ret)
    {
        LOG_ERROR("ProgressManager Initialize failed");
        return false;
    }
    // 注册下载进度回调
    auto retval = duc_srv_manager_->subscribeDownloadProgress(
        DUCType::CDC,
        [this](const DownloadProgress &progress) -> void {
            HandleDownloadProgress(DUCType::CDC, progress);
        });
    retval &= duc_srv_manager_->subscribeDownloadProgress(
        DUCType::MDC,
        [this](const DownloadProgress &progress) -> void {
            HandleDownloadProgress(DUCType::MDC, progress);
        });
    retval &= duc_srv_manager_->subscribeDownloadProgress(
        DUCType::ZCU,
        [this](const DownloadProgress &progress) -> void {
            HandleDownloadProgress(DUCType::ZCU, progress);
        });
    return retval;
}

bool DownloadManager::CheckDownloadCondition(/* TODO add dowload check arg */)
{
    DownloadConditionLists downloadConditions;
    DownloadRequirement downloadReq;
    downloadReq.deviceId("dv001");
    downloadReq.diskRequirement(500);
    downloadConditions.downloadRequirementLists().push_back(downloadReq);

    CommonStatus cond_result;
    auto retval = duc_srv_manager_->checkDownloadCondition(DUCType::CDC,
                                                           downloadConditions,
                                                           cond_result);
    if (retval != ReturnCode::OK)
    {
        LOG_ERROR("CDC checkDownloadCondition invoke failed");
        return false;
    }
    if (!HandleDownloadStageErr(DUCType::CDC, cond_result))
    {
        LOG_ERROR("Handle %s download condition failed",
                  ducTypeToString(DUCType::CDC).c_str());
        return false;
    }

    // TODO add MDC and ZCU download condition


    LOG_INFO("Check download condition succeed");

    return true;
}

bool DownloadManager::StartDownload(/* TODO add dowload packzge arg */)
{
    DownloadTaskLists tasks;
    DownloadTaskInfo task;
    task.taskId("TASK001");
    task.packageVersion("1.0.0");
    task.packageName("test_package");
    task.packageUrl("http://test.com/package");
    task.packageSize("1024");
    task.packageMd5("md5sum");
    tasks.taskLists().push_back(task);

    //向进度拟合管理器添加要下载的包
    progress_manager_->AddDownloadPackage(DUCType::CDC, tasks.taskLists());
    progress_manager_->AddDownloadPackage(DUCType::MDC, tasks.taskLists());
    progress_manager_->AddDownloadPackage(DUCType::ZCU, tasks.taskLists());

    auto retval = duc_srv_manager_->startDownload(DUCType::CDC, tasks);
    LOG_INFO("startDownload invoke retval: %d", static_cast<int>(retval));
    retval = duc_srv_manager_->startDownload(DUCType::MDC, tasks);
    LOG_INFO("startDownload invoke retval: %d", static_cast<int>(retval));
    retval = duc_srv_manager_->startDownload(DUCType::ZCU, tasks);
    LOG_INFO("startDownload invoke retval: %d", static_cast<int>(retval));
    if (retval != ReturnCode::OK)
    {
        LOG_ERROR("startDownload invoke failed");
        return false;
    }

    // TODO add CDC ZCU ... Start download
    return true;
}

bool DownloadManager::StopDownload()
{
    auto retval =
        duc_srv_manager_->downloadCtrl(DUCType::CDC, DownloadCtrl::PAUSE);
    LOG_INFO("downloadCtrl stop invoke retval: %d", static_cast<int>(retval));
    // TODO add CDC ZCU ... Stop download
    return (retval == ReturnCode::OK) ? true : false;
}

bool DownloadManager::ResumeDownload()
{
    auto retval =
        duc_srv_manager_->downloadCtrl(DUCType::CDC, DownloadCtrl::RESUME);
    LOG_INFO("downloadCtrl resume invoke retval: %d", static_cast<int>(retval));
    // TODO add CDC ZCU ... Resume download
    return (retval == ReturnCode::OK) ? true : false;
}

bool DownloadManager::CancelDownload()
{
    auto retval =
        duc_srv_manager_->downloadCtrl(DUCType::CDC, DownloadCtrl::CANCEL);
    LOG_INFO("downloadCtrl cancel invoke retval: %d", static_cast<int>(retval));
    // TODO add CDC ZCU ... Cancel download
    return (retval == ReturnCode::OK) ? true : false;
}

void DownloadManager::HandleDownloadProgress(DUCType type,
                                             const DownloadProgress &progress)
{
    LOG_INFO("recv %s download progress info: ", ducTypeToString(type).c_str());
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  package name: %s, progress: %d%%, state: %d",
                 info.packageName().c_str(),
                 info.progressPercent(),
                 static_cast<uint32_t>(info.status().status()));

        // 如果检测到某一个包下载失败，抛出异常，后续处理需要暂停所有下载动作
        if (!HandleDownloadStageErr(type, info.status()))
        {
            LOG_ERROR("package name %s download failed",
                      info.packageName().c_str());
            return;
        }
    }
    //进度拟合
    progress_manager_->onDownloadProgress(type, progress);
}

bool DownloadManager::HandleDownloadStageErr(DUCType type,
                                             const CommonStatus &result)
{
    LOG_INFO("Handle %s download stage err..", ducTypeToString(type).c_str());
    if ((result.status() == Status::kStatusFailed))
    {
        auto reason = static_cast<uint32_t>(result.reason());
        LOG_ERROR("Check download stage failed, reason: %u", reason);
        auto convert_reason = ReasonConvert(type, reason);
        if (ReasonTag(convert_reason).IsValid() && fail_reason_callback_)
        {
            fail_reason_callback_(convert_reason);
        }
        return false;
    }
    return true;
}

uint32_t DownloadManager::ReasonConvert(DUCType type, uint32_t reason)
{
    LOG_INFO("reason: %u", reason);
    auto domain{ReasonTag::Domain::kDomainNR};
    switch (type)
    {
    case DUCType::CDC:
    {
        domain = ReasonTag::Domain::kDomainCDC;
        break;
    }
    case DUCType::MDC:
    {
        domain = ReasonTag::Domain::kDomainMDC;
        break;
    }
    case DUCType::ZCU:
    {
        domain = ReasonTag::Domain::kDomainZCU;
        break;
    }
    default:
        LOG_ERROR("Unknown duc type: %d", static_cast<int32_t>(type));
        return ReasonTag::kInvalidIndex;
    }
    auto result = ReasonTag(reason, domain).GetValue();
    return result;
}

} // namespace fotamaster
} // namespace seres