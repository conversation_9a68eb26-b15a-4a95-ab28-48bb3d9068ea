#include "ev_loop/eventloop_manager.h"
#include "progress_fitting/download_progress_fitting.h"

#include <algorithm>
#include <cmath>

namespace seres
{
namespace fotamaster
{
bool DownloadProgressManager::Initialize(
    DownloadOverallProgressCallback progressCallback,
    bool needSub)
{
    return true;
}

bool DownloadProgressManager::AddDownloadPackage(
    DUCType ducType,
    const std::vector<DownloadTaskInfo> &inventorys)
{
    return true;
}

void DownloadProgressManager::onDownloadProgress(
    DUCType ducType,
    const DownloadProgress &progress)
{
}

void DownloadProgressManager::onTimerCallback()
{
}

void DownloadProgressManager::Reset()
{
}

bool DownloadProgressManager::subscribeTopic()
{
    return true;
}

} // namespace fotamaster
} // namespace seres