#include "dds_service_manager/duc_service_manager.h"
#include "handle_reason.h"
#include "logger/logger.h"
#include "upgrade_manager.h"
#include <cassert>

namespace seres
{
namespace fotamaster
{

UpgradeManager::UpgradeManager(DUCServiceManager *duc_service_manager)
    : duc_srv_manager_{duc_service_manager}
{
    progress_manager_ = &base::Singleton<UpgradeProgressManager>::Instance();
    assert(progress_manager_);
    assert(Initialize());
}

bool UpgradeManager::CheckUpgradeCondition()
{
    // 更新条件检查
    auto retval = duc_srv_manager_->checkUpdateCondition(DUCType::CDC);
    LOG_INFO("checkUpdateCondition invoke retval: %d",
             static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

bool UpgradeManager::StartUpgrade(const UpgradeMode &upgrade_mode)
{
    UpdateMode mode = UpdateMode::FormalMode;
    if (upgrade_mode == UpgradeMode::kSeamlessUpgrade)
    {
        mode = UpdateMode::SeamlessMode;
    }

    UpdateDeviceList updateList;
    std::string deviceId = "TEST001";
    updateList.updateDeviceLists().push_back(deviceId);

    //向进度拟合管理器添加要升级的设备
    progress_manager_->AddUpdateDevice(DUCType::CDC,
                                       updateList.updateDeviceLists());

    auto retval = duc_srv_manager_->startUpdate(DUCType::CDC, mode, updateList);
    if (retval != ReturnCode::OK)
    {
        LOG_ERROR("startDownload invoke failed");
        return false;
    }
    //开始定时，周期性计算总体进度并触发回调
    progress_manager_->Start();

    LOG_INFO("startUpdate invoke retval: %d", static_cast<int>(retval));
    return true;
}

bool UpgradeManager::CancelUpgrade()
{
    return false;
}

bool UpgradeManager::Initialize()
{
    //初始化进度拟合管理器
    auto ret = progress_manager_->Initialize(
        [this](uint8_t progress,
               bool allFinished,
               std::vector<EachUpdateProgressInfo> eachProgress) {
            if (progress_callback_)
                this->progress_callback_(progress);
            else
                LOG_ERROR("Update overall progress callback not registered");

            if (allFinished)
            {
                duc_srv_manager_->subscribeUpdateProgress(DUCType::CDC,
                                                          nullptr,
                                                          true);
                progress_manager_->Reset();
                progress_manager_->Stop();
            }
        },
        0.5);
    if (!ret)
    {
        LOG_ERROR("ProgressManager Initialize failed");
        return false;
    }
    // 注册下载进度回调
    auto retval = duc_srv_manager_->subscribeUpdateProgress(
        DUCType::CDC,
        [this](const UpdateProgress &progress) -> void {
            HandleUpgradeProgress(DUCType::CDC, progress);
        });

    retval &= duc_srv_manager_->subscribeCheckUpdateConditionResult(
        DUCType::CDC,
        [this](const CommonStatus &result) -> void {
            if (!HandleUpgradeStageErr(DUCType::CDC, result))
            {
                LOG_ERROR("Handle %s upgrade condition failed",
                          ducTypeToString(DUCType::CDC).c_str());
            }
        });
    return retval;
}

void UpgradeManager::HandleUpgradeProgress(DUCType type,
                                           const UpdateProgress &progress)
{
    LOG_INFO("recv %s upgrade progress info: ", ducTypeToString(type).c_str());
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  device: %s, progress: %d%%, status: %d",
                 info.deviceName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status().status()));
        // 如果检测到某一件升级失败，抛出异常，后续处理需要重试或者回滚
        if (!HandleUpgradeStageErr(type, info.status()))
        {
            LOG_ERROR("device name %s upgrade failed",
                      info.deviceName().c_str());
            // TODO 重试 or 回滚
        }
    }
    // 进度拟合
    progress_manager_->onUpdateProgress(type, progress);
}

bool UpgradeManager::HandleUpgradeStageErr(DUCType type,
                                           const CommonStatus &result)
{
    LOG_INFO("Handle %s upgrade stage err..", ducTypeToString(type).c_str());
    if ((result.status() == Status::kStatusFailed) && fail_reason_callback_)
    {
        auto reason = static_cast<uint32_t>(result.reason());
        LOG_ERROR("Check upgrade stage failed, reason: %u", reason);
        auto convert_reason = ReasonConvert(type, reason);
        if (ReasonTag(convert_reason).IsValid())
        {
            fail_reason_callback_(convert_reason);
        }
        return false;
    }
    return true;
}

uint32_t UpgradeManager::ReasonConvert(DUCType type, uint32_t reason)
{
    LOG_INFO("reason: %u", reason);
    auto domain{ReasonTag::Domain::kDomainNR};
    switch (type)
    {
    case DUCType::CDC:
    {
        domain = ReasonTag::Domain::kDomainCDC;
        break;
    }
    case DUCType::MDC:
    {
        domain = ReasonTag::Domain::kDomainMDC;
        break;
    }
    case DUCType::ZCU:
    {
        domain = ReasonTag::Domain::kDomainZCU;
        break;
    }
    default:
        LOG_ERROR("Unknown duc type: %d", static_cast<int32_t>(type));
        return ReasonTag::kInvalidIndex;
    }
    auto result = ReasonTag(reason, domain).GetValue();
    return result;
}

} // namespace fotamaster
} // namespace seres
