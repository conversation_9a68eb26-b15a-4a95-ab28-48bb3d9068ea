#include "state_machine/ota_state_machine.h"
#include "base/classfactory.h"
#include "base/singleton.h"
#include "logger/logger.h"
#include "state_machine/ota_state_snapshot.h"
#include <chrono>
#include <vector>

namespace seres
{
namespace fotamaster
{

static std::string current_time()
{
    // 获取当前系统时间
    auto now = std::chrono::system_clock::now();
    auto now_time = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                  now.time_since_epoch()) %
              1000;

    // 使用 std::put_time 格式化日期和时间
    std::tm tm = *std::localtime(&now_time);
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();
    return oss.str();
}

OtaStateMachine::OtaStateMachine(EventLoop *event_loop)
    : StateMachine(event_loop)
{
    ota_state_snapshot_ = std::make_unique<OtaStateSnapshot>();
    assert(ota_state_snapshot_);
}

OtaStateMachine::~OtaStateMachine()
{
}

void OtaStateMachine::InitState()
{
    auto state_name_vec = base::Singleton<base::ClassFactoryManager>::Instance()
                              .GetRegisteredClasses<State>();
    LOG_INFO("Registered states size: %lu", state_name_vec.size());

    for (const auto &state_name : state_name_vec)
    {
        LOG_INFO("Machine add state: %s", state_name.c_str());
        AddState(state_name, this);
    }
}

void OtaStateMachine::ChangeState(const std::string &new_state)
{
    if ((GetCurrentState() == new_state))
    {
        LOG_WARN("request same state, no change!!!");
        return;
    }

    StateMachine::ChangeState(new_state);
}

void OtaStateMachine::HandleEvent(const OtaStateMsg &ota_info)
{
    if (ota_info.in_state == ota_state_msg_.in_state)
    {
        LOG_INFO("request same state, no handle!!!");
        return;
    }

    ota_state_msg_ = ota_info;

    auto curr_time = current_time();
    LOG_INFO("Handle event in state: %s, time: %s",
             g_ota_state_to_str.at(ota_info.in_state).c_str(),
             curr_time.c_str());

    // 存状态快照(下载未完成、无感安装未完成、未开始正式升级)
    if ((ota_info.in_state == OtaInState::kOtaInDownloadState) ||
        (ota_info.in_state == OtaInState::kOtaInSeamlessUpgradeState) ||
        (ota_info.in_state == OtaInState::kOtaInPreFormalUpgradeState))
    {
        OtaStateSnapshotInfo state_snapshot_info;
        state_snapshot_info.time = std::move(curr_time);
        state_snapshot_info.state_info = ota_info;
        ota_state_snapshot_->TakeSnapshot(state_snapshot_info);
    }

    EventData event_data;
    event_data.data = ota_info.in_state;
    GetState(GetCurrentState())->Process(event_data);
}

bool OtaStateMachine::EventCallback(const StateEvent &state,
                                    const std::any &data)
{
    if (!event_cb_)
    {
        LOG_ERROR("event callback not registered");
        return false;
    }
    return event_cb_(state, data);
}

} // namespace fotamaster
} // namespace seres