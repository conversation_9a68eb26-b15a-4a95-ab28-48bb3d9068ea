#include "state_machine/pre_formal_upgrade_state.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

bool PreFormalUpgradeState::Enter()
{
    LOG_INFO("=====Enter pre formal upgrade state");
    if (!GetStateMachine()->EventCallback(StateEvent::kCheckPreCondition))
    {
        LOG_ERROR("Call start check precondition failed");
        return false;
    }
    return true;
}

void PreFormalUpgradeState::Process(const EventData &data)
{
    LOG_INFO("=====Process pre formal upgrade state");
    HandleRequestEvent(std::any_cast<OtaInState>(data.data));
}

void PreFormalUpgradeState::Exit()
{
    LOG_INFO("=====Exit pre formal upgrade state");
}

void PreFormalUpgradeState::HandleRequestEvent(const OtaInState &ota_in_state)
{
    switch (ota_in_state)
    {
    case OtaInState::kOtaInIdleState:
    {
        GetStateMachine()->ChangeState("IdleState");
        break;
    }
    case OtaInState::kOtaInFormalUpgradeState:
    {
        GetStateMachine()->ChangeState("FormalUpgradeState");
        break;
    }
    case OtaInState::kOtaInFaultState:
    {
        GetStateMachine()->ChangeState("FaultState");
        break;
    }
    default:
    {
        auto iter = g_ota_state_to_str.find(ota_in_state);
        if (iter != g_ota_state_to_str.end())
        {
            LOG_WARN("Current ota in state[PreFormalUpgradeState], not support change to state[%s]",
                     iter->second.c_str());
        }
        else
        {
            LOG_WARN("Unkown ota in state: %hhu",
                    static_cast<uint8_t>(ota_in_state));
        }
        break;
    }
    }
}

REGISTER_STATE("PreFormalUpgradeState", PreFormalUpgradeState)

} // namespace fotamaster
} // namespace seres