#include "state_machine/seamless_upgrade_state.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

bool SeamlessUpgradeState::Enter()
{
    LOG_INFO("=====Enter seamless upgrade state state");
    if (!GetStateMachine()->EventCallback(StateEvent::kStartSeamlessUpgrade))
    {
        LOG_ERROR("Call start seamless upgrade failed");
        return false;
    }
    return true;
}

void SeamlessUpgradeState::Process(const EventData &data)
{
    LOG_INFO("=====Process seamless upgrade state");
    HandleRequestEvent(std::any_cast<OtaInState>(data.data));
}

void SeamlessUpgradeState::Exit()
{
    LOG_INFO("=====Exit seamless upgrade state");
}

void SeamlessUpgradeState::HandleRequestEvent(const OtaInState &ota_in_state)
{
    switch (ota_in_state)
    {
    case OtaInState::kOtaInFaultState:
    {
        GetStateMachine()->ChangeState("FaultState");
        break;
    }
    case OtaInState::kOtaInSeamlessUpgradeFinishState:
    {
        GetStateMachine()->ChangeState("SeamlessUpgradeFinishState");
        break;
    }
    default:
    {
        auto iter = g_ota_state_to_str.find(ota_in_state);
        if (iter != g_ota_state_to_str.end())
        {
            LOG_WARN("Current ota in state[SeamlessUpgradeState], not support change to state[%s]",
                     iter->second.c_str());
        }
        else
        {
            LOG_WARN("Unkown ota in state: %hhu",
                    static_cast<uint8_t>(ota_in_state));
        }
        break;
    }
    }
}

REGISTER_STATE("SeamlessUpgradeState", SeamlessUpgradeState)

} // namespace fotamaster
} // namespace seres