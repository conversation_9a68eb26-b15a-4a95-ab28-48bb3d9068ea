#include "state_machine/ota_state_snapshot.h"
#include "logger/logger.h"
#include <fstream>
#include <filesystem>

namespace seres
{
namespace fotamaster
{

OtaStateSnapshot::OtaStateSnapshot(const std::string &history_path)
{
    if (history_path.empty())
    {
        history_path_ =
            OTA_CFG_FILE_PATH_PERFIX + std::string("/ota_state_snapshot");
    }
    else
    {
        history_path_ = std::move(history_path);
    }
    LOG_INFO("ota state snapshot file: %s", history_path_.c_str());
}

bool OtaStateSnapshot::TakeSnapshot(const OtaStateSnapshotInfo &snapshot_info)
{
    std::ofstream out_file(history_path_,
                           std::ios::binary | std::ios::out | std::ios::trunc);
    if (!out_file.is_open())
    {
        LOG_ERROR("Open file %s failed", history_path_.c_str());
        return false;
    }

    OrderedJson j;
    j[kOtaStateSnapshotKey] = snapshot_info;

    // 写入json数据
    out_file << j.dump();

    // 关闭文件
    out_file.close();

    return true;
}

std::optional<OtaStateSnapshotInfo> OtaStateSnapshot::GetSnapshot()
{
    std::optional<OtaStateSnapshotInfo> retval{std::nullopt};
    if (!std::filesystem::exists(history_path_))
    {
        LOG_DEBUG("File %s is not exist", history_path_.c_str());
        return retval;
    }

    std::ifstream in_file(history_path_, std::ios::binary | std::ios::in);
    if (!in_file.is_open())
    {
        LOG_ERROR("Open file %s failed", history_path_.c_str());
        return retval;
    }

    OrderedJson j;
    in_file >> j; // 读取 JSON
    LOG_INFO("get snapshot info: %s", j.dump().c_str());

    OtaStateSnapshotInfo snapshot_info;
    // snapshot_info = j.at(kOtaStateSnapshotKey).get<OtaStateSnapshotInfo>(); // 自动调用 from_json
    j.at(kOtaStateSnapshotKey).get_to(snapshot_info); // 自动调用 from_json

    in_file.close();

    return std::optional<OtaStateSnapshotInfo>{snapshot_info};
}

} // namespace fotamaster
} // namespace seres