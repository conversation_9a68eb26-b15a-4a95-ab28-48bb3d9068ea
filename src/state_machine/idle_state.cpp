#include "state_machine/idle_state.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"
namespace seres
{
namespace fotamaster
{

bool IdleState::Enter()
{
    LOG_INFO("=====Enter idle state");
    return true;
}

void IdleState::Process(const EventData &data)
{
    LOG_INFO("=====Process idle state");
    HandleRequestEvent(std::any_cast<OtaInState>(data.data));
}

void IdleState::Exit()
{
    LOG_INFO("=====Exit idle state");
}

void IdleState::HandleRequestEvent(const OtaInState &ota_in_state)
{
    if (ota_in_state == OtaInState::kOtaInDownloadState)
    {
        GetStateMachine()->ChangeState("DownloadState");
    }
    else
    {
        auto iter = g_ota_state_to_str.find(ota_in_state);
        if (iter != g_ota_state_to_str.end())
        {
            LOG_WARN("Current ota in state[IdleState], not support change to state[%s]",
                     iter->second.c_str());
        }
        else
        {
            LOG_WARN("Unkown ota in state: %hhu",
                    static_cast<uint8_t>(ota_in_state));
        }
    }
}

REGISTER_STATE("IdleState", IdleState)

} // namespace fotamaster
} // namespace seres