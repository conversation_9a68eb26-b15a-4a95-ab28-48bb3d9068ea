#include "state_machine/download_state.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

bool DownloadState::Enter()
{
    LOG_INFO("=====Enter download state");
    if (!GetStateMachine()->EventCallback(StateEvent::kStartDownload))
    {
        LOG_ERROR("Call start download failed");
        return false;
    }
    return true;
}

void DownloadState::Process(const EventData &data)
{
    LOG_INFO("=====Process download state");
    HandleRequestEvent(std::any_cast<OtaInState>(data.data));
}

void DownloadState::Exit()
{
    LOG_INFO("=====Exit download state");
}

void DownloadState::HandleRequestEvent(const OtaInState &ota_in_state)
{
    switch (ota_in_state)
    {
    case OtaInState::kOtaInFaultState:
    {
        GetStateMachine()->ChangeState("FaultState");
        break;
    }
    case OtaInState::kOtaInSeamlessUpgradeState:
    {
        GetStateMachine()->ChangeState("SeamlessUpgradeState");
        break;
    }
    case OtaInState::kOtaInSeamlessUpgradeFinishState:
    {
        GetStateMachine()->ChangeState("SeamlessUpgradeFinishState");
        break;
    }
    default:
    {
        auto iter = g_ota_state_to_str.find(ota_in_state);
        if (iter != g_ota_state_to_str.end())
        {
            LOG_WARN("Current ota in state[DownloadState], not support change to state[%s]",
                     iter->second.c_str());
        }
        else
        {
            LOG_WARN("Unkown ota in state: %hhu",
                    static_cast<uint8_t>(ota_in_state));
        }
        break;
    }
    }
}

REGISTER_STATE("DownloadState", DownloadState)

} // namespace fotamaster
} // namespace seres