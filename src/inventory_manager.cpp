#include "inventory_manager.h"
#include "base/singleton.h"
#include "ev_loop/eventloop_manager.h"
#include "logger/logger.h"
#include <algorithm>
#include <cassert>

namespace seres
{
namespace fotamaster
{

InventoryManager::InventoryManager(DUCServiceManager *duc_service_manager)
    : duc_srv_manager_(duc_service_manager)
{
    main_loop_ = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    assert(main_loop_ != nullptr);

    std::string dev_config_path =
        OTA_CFG_FILE_PATH_PERFIX + std::string("/ota_device_lists.json");
    LOG_INFO("dev_config_path: %s", dev_config_path.c_str());
    DeviceListRecoderConfig dev_config(std::move(dev_config_path));
    auto retval = dev_config.GetDeviceListConfig(all_dev_list_);
    assert(retval);

    assert(Initialize());
}

bool InventoryManager::TriggerInventoryCollection()
{
    SelectedInventoryList inventoryList;
    for (const auto &device_name : all_dev_list_.cdc_dev_list)
    {
        LOG_INFO("CDC device: %s", device_name.c_str());
        inventoryList.inventoryLists().push_back(device_name);
    }
    auto retval = StartInventoryCollection(DUCType::CDC, inventoryList);
    // TODO 添加MDC和ZCU的资产收集
    return retval;
}

bool InventoryManager::StopInventoryCollection()
{
    inventory_info_list_.clear();
    try_count_map_[DUCType::CDC] = 2;
    auto retval = duc_srv_manager_->stopInventoryCollection(DUCType::CDC);
    LOG_INFO("stopInventoryCollection invoke retval: %d",
             static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

bool InventoryManager::Initialize()
{
    try_count_map_ = {{DUCType::CDC, 2}, {DUCType::MDC, 2}, {DUCType::ZCU, 2}};

    auto retval = duc_srv_manager_->subscribeInventoryResult(
        DUCType::CDC,
        [this](const InventoryResult &result) -> void {
            LOG_INFO("Recv CDC inventory result:");
            for (const auto &info : result.InventoryLists())
            {
                LOG_INFO("  ECU: %s, version: %s",
                         info.ecuName().c_str(),
                         info.softwareVersion().c_str());
            }
            HandleInventoryResult(DUCType::CDC, result);
        });
    // TODO 监听MDC和ZCU的资产收集回调
    return retval;
}

bool InventoryManager::StartInventoryCollection(
    DUCType type,
    const SelectedInventoryList &inventory_list)
{
    LOG_INFO("=================Start inventoryCollection ===============");
    auto retval = duc_srv_manager_->inventoryCollection(type, inventory_list);
    LOG_INFO("inventoryCollection invoke retval: %d", static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

void InventoryManager::HandleInventoryResult(DUCType type,
                                             const InventoryResult &result)
{
    main_loop_->RunInLoopThread([this, type, result]() -> void {
        assert(main_loop_->IsDefaultLoop());

        // 先将收集到的资产信息存入缓存
        SaveInventoryResult(result);

        if (try_count_map_[type] > 0)
        {
            if (!CheckInventory(type, result))
            {
                try_count_map_[type]--;
            }
            else
            {
                LOG_INFO("CDC inventory collection finished.");
                try_count_map_[type] = 0;
            }
        }
        else
        {
            LOG_INFO("cdc_try_count_: %hhu", try_count_map_[type]);
            // 触发资产上报
            ReportInventoryFunc();
        }
    });
}

bool InventoryManager::CheckInventory(DUCType type,
                                      const InventoryResult &result)
{
    // 检查是否采集完毕
    auto diff = CheckInventoryResult(all_dev_list_.cdc_dev_list, result);
    if (diff.has_value())
    {
        // 还有资产没有获取到,继续获取
        auto dev_list = diff.value();
        SelectedInventoryList inventoryList;
        for (const auto &device_name : dev_list)
        {
            LOG_INFO("Retry CDC device: %s", device_name.c_str());
            inventoryList.inventoryLists().push_back(device_name);
        }
        StartInventoryCollection(type, inventoryList);
        return false;
    }
    return true;
}

auto InventoryManager::CheckInventoryResult(
    const std::vector<std::string> &dev_list,
    const InventoryResult &result) -> std::optional<std::vector<std::string>>
{
    std::optional<std::vector<std::string>> retval{std::nullopt};
    auto inventory_list = result.InventoryLists();
    LOG_INFO("dev_list size: %lu, inventory_list size: %lu",
             dev_list.size(),
             inventory_list.size());
    if (inventory_list.size() != dev_list.size())
    {
        std::vector<std::string> dev_names;
        dev_names.reserve(inventory_list.size());
        for (const auto &inventory : inventory_list)
        {
            dev_names.push_back(inventory.ecuName());
        }
        std::sort(dev_names.begin(), dev_names.end());

        std::vector<std::string> diff;
        // 计算对称差集（仅存在于一个容器中的元素）
        std::set_symmetric_difference(dev_list.begin(),
                                      dev_list.end(),
                                      dev_names.begin(),
                                      dev_names.end(),
                                      std::back_inserter(diff));
        if (diff.size() > 0)
        {
            LOG_INFO("diff dev list size: %lu", diff.size());
            return std::optional<std::vector<std::string>>{diff};
        }
    }
    return retval;
}

void InventoryManager::SaveInventoryResult(const InventoryResult &result)
{
    LOG_WARN("------------------ SaveInventoryResult");
    ComponentInfoInternal component{};
    for (const auto &iter : result.InventoryLists())
    {
        component.partNumber = iter.partNumber();
        component.softwareVersion = iter.softwareVersion();
        component.supplierCode = iter.supplierCode();
        component.ecuName = iter.ecuName();
        component.serialNumber = iter.serialNumber();
        component.hardwareVersion = iter.hardwareVersion();
        component.ecuBatchNumber = iter.ecuBatchNumber();
        component.bootloaderVersion = iter.bootloaderVersion();
        component.backupVersion = iter.backupVersion();
        inventory_info_list_.insert(std::make_pair(component.ecuName, component));
    }
}

void InventoryManager::ReportInventoryFunc()
{
    // 当都收集完成或者try次数为0时才启动上报资产
    if ((try_count_map_[DUCType::CDC] == 0) &&
        /*(try_count_map_[DUCType::MDC] == 0) &&
        (try_count_map_[DUCType::ZCU] == 0) && */
        inventory_callback_)
    {
        LOG_WARN("ReportInventory");
        inventory_callback_(inventory_info_list_);
    }
}

} // namespace fotamaster
} // namespace seres