#include "fota_master_manager.h"
#include "OTA_VucServiceData.hpp"
#include "base/threadinfo.h"
#include "dds_service_manager/duc_service_manager.h"
#include "download_manager.h"
#include "inventory_manager.h"
#include "logger/logger.h"
#include <cassert>

namespace seres
{
namespace fotamaster
{

using namespace seres::ota_vuc_service;

FOTAMasterManager::FOTAMasterManager(EventLoop *event_loop)
    : event_loop_{event_loop}
{
    // TODO 车云建链
    // ...

    // 创建状态机
    InitStateMachine();

    // 初始化dds service
    assert(InitVucSubscriber());
    assert(InitVucPublisher());
    assert(InitDucServiceManager());


    // 创建资产管理者
    InitInventoryManager();

    InitDownloadManager();
}

FOTAMasterManager::~FOTAMasterManager()
{
}

bool FOTAMasterManager::TriggerInventoryCollection()
{
    return inventory_manager_->TriggerInventoryCollection();
}

bool FOTAMasterManager::InitVucSubscriber()
{
    auto sub_result =
        dds_wrapper::VucSubscriber::Create(kVucDomainID, kVucServiceTopicName);
    if (!sub_result)
    {
        LOG_ERROR("Create vuc service subscriber failed, error: %s",
                  sub_result.error_msg.c_str());
        return false;
    }
    LOG_INFO("Create vuc service subscriber success");
    vuc_service_sub_ = sub_result.GetValue();

    auto result = vuc_service_sub_->Subscribe<UpgradeTask>(
        [this](const UpgradeTask &msg) -> void {
            LOG_INFO("Subscriber thread id: %d", base::curthread::tid());
            LOG_INFO("Recv: upgrade info = %s, , package info = %s",
                     msg.upgradeInfo().c_str(),
                     msg.upgradePackageInfo().c_str());

            OtaTaskInfo task_info;
            task_info.upgrade_info = std::move(msg.upgradeInfo());
            task_info.package_info = std::move(msg.upgradePackageInfo());
            OtaStateMsg state_msg;
            state_msg.in_state = OtaInState::kOtaInDownloadState;
            state_msg.state_param = task_info;
            event_loop_->RunInLoopThread(
                [this, state_msg = std::move(state_msg)]() -> void {
                    LOG_INFO("event_loop thread id: %d, is main thread: %s",
                             base::curthread::tid(),
                             base::curthread::is_main_thread() ? "true"
                                                               : "false");

                    // 当前处于Idle状态
                    ota_state_machine_->HandleEvent(state_msg);
                });
        });
    if (!result)
    {
        LOG_ERROR("Subscribe topic[upgrade_task_notify] failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }

    result = vuc_service_sub_->Subscribe<SeamlessUpgradeMode>(
        [this](const SeamlessUpgradeMode &msg) -> void {
            LOG_INFO("Subscriber thread id: %d", base::curthread::tid());
            LOG_INFO("seamless_upgrade_mode: %d",
                     static_cast<int32_t>(msg.use_seamless_upgrade()));

            // TODO 持久化是否进行无感升级
            bool support{false};
            if (msg.use_seamless_upgrade() == UseSeamlessUpgrade::kYes)
            {
                support = true;
            }
            support_seamless_upgrade_.store(support, std::memory_order_release);
        });
    if (!result)
    {
        LOG_ERROR("Subscribe topic[seamless_upgrade_mode] failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }

    result = vuc_service_sub_->Subscribe<
        UpgradeModeCtrl>([this](const UpgradeModeCtrl &msg) -> void {
        LOG_INFO("upgrade_mode: %d", static_cast<int32_t>(msg.upgrade_mode()));
        if (msg.upgrade_mode() == UpgradeModeE::kImmediately)
        {
            //TODO 立即升级
            LOG_INFO("User trigger upgrade immediately");
            event_loop_->RunInLoopThread([this]() -> void {
                OtaStateMsg state_msg;
                state_msg.in_state = OtaInState::kOtaInPreFormalUpgradeState;

                // 当前应处于SeamlessUpgradeFinishState状态
                ota_state_machine_->HandleEvent(state_msg);
            });
        }
        else
        {
            LOG_INFO("User trigger upgrade appointment, time: %s",
                     msg.time().c_str());

            //TODO 预约升级，启动定时器定时，定时时间到触发升级
            // event_loop_->RunInLoopThread([this]() -> void {
            //     OtaStateMsg state_msg;
            //     state_msg.in_state = OtaInState::kOtaInPreFormalUpgradeState;

            //     // 当前应处于SeamlessUpgradeFinishState状态
            //     ota_state_machine_->HandleEvent(state_msg);
            // });
        }
    });
    if (!result)
    {
        LOG_ERROR("Subscribe topic[upgrade_mode_ctrl] failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }

    result = vuc_service_sub_->Subscribe<UpgradeNotify>(
        [this](const UpgradeNotify &msg) -> void {
            LOG_INFO("upgrade_notify: %d",
                     static_cast<int32_t>(msg.upgrade_notify()));
            if (msg.upgrade_notify() == UpgradeNotifyE::kNotUpgrading)
            {
                //TODO 暂不升级
                LOG_INFO("User no upgrade for the time being");
                event_loop_->RunInLoopThread([this]() -> void {
                    OtaStateMsg state_msg;
                    state_msg.in_state = OtaInState::kOtaInIdleState;

                    // 当前应处于PreUpgradeState状态
                    ota_state_machine_->HandleEvent(state_msg);
                });
            }
            else
            {
                //TODO 计时时间到触发升级
                LOG_INFO("It's time to upgrade the timing");
                event_loop_->RunInLoopThread([this]() -> void {
                    OtaStateMsg state_msg;
                    state_msg.in_state = OtaInState::kOtaInFormalUpgradeState;
                    // 当前应处于PreUpgradeState状态
                    ota_state_machine_->HandleEvent(state_msg);
                });
            }
        });
    if (!result)
    {
        LOG_ERROR("Subscribe topic[upgrade_notify] failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }
    return true;
}

bool FOTAMasterManager::InitVucPublisher()
{
    auto pub_result =
        dds_wrapper::VucPublisher::Create(kVucDomainID, kVucServiceTopicName);
    if (!pub_result)
    {
        LOG_ERROR("Create vuc service publisher failed, error: %s",
                  pub_result.error_msg.c_str());
        return false;
    }
    LOG_INFO("Create vuc service publisher success");
    vuc_service_pub_ = pub_result.GetValue();

    return true;
}

bool FOTAMasterManager::InitDucServiceManager()
{
    duc_service_manager_ = std::make_shared<DUCServiceManager>();
    if (!duc_service_manager_)
    {
        LOG_ERROR("Duc service manager create failed");
        return false;
    }

    bool retval = duc_service_manager_->initialize(1);
    if (!retval)
    {
        LOG_ERROR("Duc service manager initialize failed");
        return false;
    }

    retval = duc_service_manager_->createClient(
        DUCType::CDC,
        std::string(),
        [](DUCType type, bool connected) -> void {
            LOG_INFO("service status change: type: %s, connected: %d",
                     ducTypeToString(type).c_str(),
                     connected);
        });

    if (!retval)
    {
        LOG_ERROR("Create CDC_DucService_Service failed");
        return false;
    }
    return true;
}

void FOTAMasterManager::InitStateMachine()
{
    ota_state_machine_ = std::make_unique<OtaStateMachine>(event_loop_);
    assert(ota_state_machine_);
    ota_state_machine_->InitState();
    ota_state_machine_->RegisterEventHandle(
        [this](const StateEvent &state, const std::any &data) -> bool {
            return EventHandleCb(state, data);
        });
    ota_state_machine_->ChangeState("IdleState");
}

void FOTAMasterManager::InitInventoryManager()
{
    inventory_manager_ =
        std::make_unique<InventoryManager>(duc_service_manager_.get());
    assert(inventory_manager_);
    inventory_manager_->RegisterInventoryCallback(
        [this](const InventoryManager::InventoryInfoList &info_list) -> void {
            LOG_INFO("Inventory collection finished, report...");
            // TODO 资产上报
            // if (vuc_service_pub_->IsConnected().GetValue())
            // {
            //     vuc_service_pub_->Publish<InventoryInfoList>(info_list);
            // }
        });
}

void FOTAMasterManager::InitDownloadManager()
{
    download_manager_ =
        std::make_unique<DownloadManager>(duc_service_manager_.get());
    assert(download_manager_);
    download_manager_->RegisterFailReasonCallback(
        [this](const uint32_t &reason) -> void {
            // TODO 暂停下载
            download_manager_->StopDownload();

            event_loop_->RunInLoopThread([this, reason]() -> void {
                OtaStateMsg state_msg;
                state_msg.in_state = OtaInState::kOtaInFaultState;
                state_msg.state_param = reason;
                // 当前应处于Download状态
                ota_state_machine_->HandleEvent(state_msg);
            });
        });

    download_manager_->RegisterProgressCallback(
        [this](const uint32_t &progress) -> void {
        // 进度上报
        using TotalDownloadProgress =
            seres::ota_vuc_service::TotalDownloadProgress;
        TotalDownloadProgress download_progress(progress);
        vuc_service_pub_->Publish(download_progress);

        // 下载完成，进入下一阶段
        if (progress == 100)
        {
            event_loop_->RunInLoopThread([this]() -> void {
                // TODO 如果用户同意预安装，进入预安装阶段
                OtaStateMsg state_msg;
                if (support_seamless_upgrade_.load(std::memory_order_acquire))
                {
                    state_msg.in_state = OtaInState::kOtaInSeamlessUpgradeState;
                }
                else
                {
                    state_msg.in_state =
                        OtaInState::kOtaInSeamlessUpgradeFinishState;
                }

                // 当前应处于Download状态
                ota_state_machine_->HandleEvent(state_msg);
            });
        }
    });
}

void FOTAMasterManager::InitUpgradeManager()
{
    upgrade_manager_ =
        std::make_unique<UpgradeManager>(duc_service_manager_.get());
    assert(upgrade_manager_);
    upgrade_manager_->RegisterFailReasonCallback(
        [this](const uint32_t &reason) -> void {
            event_loop_->RunInLoopThread([this, reason]() -> void {
                OtaStateMsg state_msg;
                state_msg.in_state = OtaInState::kOtaInFaultState;
                state_msg.state_param = reason;
                // 当前应处于SeamlessUpgrade or FormalUpgrade状态
                ota_state_machine_->HandleEvent(state_msg);
            });
        });
    // TODO add grogress callback
}

bool FOTAMasterManager::EventHandleCb(const StateEvent &state,
                                      const std::any &data)
{
    switch (state)
    {
    case StateEvent::kStartDownload:
    {
        LOG_INFO("Start download...");
        // UsageMode=Convenience 或 Driving 或 remote & PowerMode=On时启动下载
        return HandleStartDownload();
    }
    case StateEvent::kStartSeamlessUpgrade:
    {
        LOG_INFO("Start seamless upgrade...");
        // TODO
        break;
    }
    case StateEvent::kCheckPreCondition:
    {
        LOG_INFO("Start check precondition...");
        // TODO
        break;
    }
    case StateEvent::kStartFormalUpgrade:
    {
        LOG_INFO("Start formal upgrade...");
        // TODO
        break;
    }
    case StateEvent::kFaultHandling:
    {
        LOG_INFO("Start handle fault...");
        HandleFault();
        break;
    }
    default:
        break;
    }
    return true;
}

bool FOTAMasterManager::HandleStartDownload()
{
    // 下载前置条件检查
    // TODO 下载的前置条件提取
    auto state_msg = ota_state_machine_->GetStateMsg();
    if (state_msg.in_state != OtaInState::kOtaInDownloadState)
    {
        LOG_ERROR("State is not in DownloadState, current state: %s",
                  g_ota_state_to_str.at(state_msg.in_state).c_str());
        return false;
    }

    OtaTaskInfo task_info;
    if (std::holds_alternative<OtaTaskInfo>(state_msg.state_param))
    {
        task_info = std::get<OtaTaskInfo>(state_msg.state_param);
        LOG_INFO("upgrade info = %s, package info = %s",
                 task_info.upgrade_info.c_str(),
                 task_info.package_info.c_str());
    }

    auto retval = download_manager_->CheckDownloadCondition();
    if (!retval)
    {
        LOG_ERROR("CheckDownloadCondition invoke failed");
        return false;
    }

    // 触发下载
    retval = download_manager_->StartDownload();
    if (!retval)
    {
        LOG_ERROR("StartDownload invoke failed");
        return false;
    }
    return true;
}

bool FOTAMasterManager::HandleStartUpgrade(UpgradeMode mode)
{
    auto retval = upgrade_manager_->CheckUpgradeCondition();
    if (!retval)
    {
        LOG_ERROR("CheckUpgradeCondition invoke failed");
        return false;
    }

    upgrade_manager_->StartUpgrade(mode);
    return true;
}

bool FOTAMasterManager::HandleFault()
{
    auto state_msg = ota_state_machine_->GetStateMsg();
    if (state_msg.in_state != OtaInState::kOtaInFaultState)
    {
        LOG_ERROR("State is not in FaultState, current state: %s",
                  g_ota_state_to_str.at(state_msg.in_state).c_str());
        return false;
    }

    uint32_t reason{0u};
    if (std::holds_alternative<uint32_t>(state_msg.state_param))
    {
        reason = std::get<uint32_t>(state_msg.state_param);
        LOG_INFO("fault reason: %u", reason);
    }
    // TODO report reason

    return true;
}

} // namespace fotamaster
} // namespace seres