#!/bin/bash
BUILD_PLATFORM=$1
BUILD_TYPE=$2
EXTERN_INSTALL_PATH=$3

mkdir -p build
cd build/
rm -rf *

if [ "${BUILD_PLATFORM}" = "x86" ];then
    cmake -DSPDLOG_BUILD_SHARED=ON -DCMAKE_INSTALL_PREFIX=${EXTERN_INSTALL_PATH} -DCMAKE_BUILD_TYPE=${BUILD_TYPE} ..
else
    echo "build mt8678 platform.."
    cmake -DSPDLOG_BUILD_SHARED=ON -DCMAKE_INSTALL_PREFIX=${EXTERN_INSTALL_PATH} -DCMAKE_BUILD_TYPE=${BUILD_TYPE} -DCMAKE_TOOLCHAIN_FILE=../../cmake/yocto-toolchain.cmake ..
fi

make -j32

make install

cd ..
