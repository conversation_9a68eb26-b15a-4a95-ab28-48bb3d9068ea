set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR arm)

if(NOT DEFINED SDK_SYSROOTS)
    set(SDK_SYSROOTS /usr/bin)
endif()

set(TOOLCHAIN_PREFIX ${SDK_SYSROOTS}/aarch64-linux-gnu-)

# INCLUDE(CMakeForceCompiler)
set(CMAKE_C_COMPILER ${TOOLCHAIN_PREFIX}gcc)
# CMAKE_FORCE_C_COMPILER(${TOOLCHAIN_PREFIX}gcc GNU)
set(CMAKE_CXX_COMPILER ${TOOLCHAIN_PREFIX}g++)
# CMAKE_FORCE_CXX_COMPILER(${TOOLCHAIN_PREFIX}g++ GNU)

set(CMAKE_SYSROOT ${SDK_SYSROOTS}/aarch64-linux-gnu)

set(CMAKE_FIND_ROOT_PATH ${CMAKE_SYSROOT})
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)