#pragma once

#include "base/noncopyable.h"
#include "dds_service_manager/duc_service_manager.h"
#include "device_config.h"
#include <atomic>
#include <functional>
#include <mutex>
#include <optional>
#include <string>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{

struct ComponentInfoInternal
{
    std::string partNumber{""};
    std::string softwareVersion{""};
    std::string supplierCode{""};
    std::string ecuName{""};
    std::string serialNumber{""};
    std::string hardwareVersion{""};
    std::string ecuBatchNumber{""};
    std::string bootloaderVersion{""};
    std::string backupVersion{""};
};

class EventLoop;
class InventoryManager : base::Noncopyable
{
public:
    /* key: ecu_name, value: ComponentInfoInternal*/
    using InventoryInfoList =
        std::unordered_map<std::string, ComponentInfoInternal>;
    using InventoryCallback = std::function<void(const InventoryInfoList &)>;
    explicit InventoryManager(DUCServiceManager *duc_service_manager);

    void RegisterInventoryCallback(InventoryCallback &&callback)
    {
        inventory_callback_ = std::move(callback);
    }

    bool TriggerInventoryCollection();
    bool StopInventoryCollection();

    bool StartInventoryCollection(DUCType type,
                                  const SelectedInventoryList &inventory_list);

private:
    bool Initialize();

    void HandleInventoryResult(DUCType type, const InventoryResult &result);
    bool CheckInventory(DUCType type, const InventoryResult &result);
    std::optional<std::vector<std::string>> CheckInventoryResult(
        const std::vector<std::string> &dev_list,
        const InventoryResult &result);
    void SaveInventoryResult(const InventoryResult &result);
    void ReportInventoryFunc();

private:
    EventLoop *main_loop_{nullptr};
    DUCServiceManager *duc_srv_manager_{nullptr};
    InventoryCallback inventory_callback_{nullptr};
    ns::DeviceListConfig all_dev_list_;
    std::mutex inventory_info_list_mutex_;
    InventoryInfoList inventory_info_list_;
    std::unordered_map<DUCType, uint8_t> try_count_map_;
};

} // namespace fotamaster
} // namespace seres