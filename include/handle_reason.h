#pragma once

#include <cstdint>

namespace seres
{
namespace fotamaster
{

class ReasonTag
{
public:
    using TagValueType = uint32_t;
    static constexpr TagValueType kIndexBits{20};
    static constexpr TagValueType kIndexMask =
        ((((TagValueType)0x1) << kIndexBits) - 1);
    static constexpr TagValueType kInvalidIndex = kIndexMask;

    enum class Domain : TagValueType
    {
        kDomainCDC = 1u,
        kDomainMDC,
        kDomainZCU,
        kDomainNR
    };

    ReasonTag(TagValueType reason, Domain domain)
    {
        reason &= kIndexMask;
        tag_ = reason | (static_cast<TagValueType>(domain) << kIndexBits);
    }

    ReasonTag(TagValueType reason, TagValueType domain)
    {
        reason &= kIndexMask;
        tag_ = reason | (domain << kIndexBits);
    }

    ReasonTag(TagValueType tag) : tag_(tag)
    {
    }
    TagValueType GetReason()
    {
        return tag_ & kIndexMask;
    }
    TagValueType GetValue()
    {
        return tag_;
    }
    bool IsValid()
    {
        return GetReason() != kInvalidIndex;
    }

private:
    TagValueType tag_{0u};
};

} // namespace fotamaster
} // namespace seres