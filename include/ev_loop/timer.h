#pragma once

#include "ev_loop/error_code.h"
#include "ev_loop/timeout_handler.h"
#include <functional>
#include <optional>

namespace seres
{
namespace fotamaster
{

class EventLoop;

/// @brief  a timer run in EventLoop.
///
/// @note   Do NOT use this in two EventLoop (thread)
///
class Timer : private TimeoutHandler
{
public:
    using TimeoutCallback = std::function<void()>;

    explicit Timer(EventLoop *loop);
    virtual ~Timer();

    /// @brief	Set the Callback object
    ///
    /// @param	callback
    /// @return	std::optional<ErrCode> 失败时有错误码值，成功没有值
    ///
    std::optional<ErrCode> SetCallback(TimeoutCallback &&callback);

    /// @brief  Start timer and the callback will be called after timeout
    ///
    /// @param	timeout - unit second
    /// @return	std::optional<ErrCode> 失败时有错误码值，成功没有值
    ///
    /// @note   the callback must be set before this function is called.
    ///
    std::optional<ErrCode> After(double timeout);

    /// @brief  Start timer and the callback will be called Every timeout
    ///
    /// @param	timeout - unit second
    /// @return	std::optional<ErrCode> 失败时有错误码值，成功没有值
    ///
    /// @note   the callback must be set before this function is called.
    ///
    std::optional<ErrCode> Every(double timeout);

    void Cancel();

    using TimeoutHandler::loop;

private:
    void OnTimeout() override;

private:
    TimeoutCallback on_timeout_{};
};

} // namespace fotamaster
} // namespace seres
