#pragma once

#include "OTA_DucData.hpp"
#include "base/noncopyable.h"
#include "base/singleton.h"
#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/timer.h"
#include "logger/logger.h"

#include <atomic>
#include <chrono>
#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{

using namespace seres::ota_duc_service;

// 进度回调函数类型
using DownloadOverallProgressCallback =
    std::function<void(uint8_t progress,
                       uint64_t allPakageSize,
                       uint32_t speed,
                       uint32_t remainingTime)>;

class DownloadProgressManager : public base::Noncopyable
{
public:
    DownloadProgressManager() = default;
    ~DownloadProgressManager();

    bool Initialize(DownloadOverallProgressCallback progressCallback,
                    bool needSub = false);

    bool AddDownloadPackage(DUCType ducType,
                            const std::vector<DownloadTaskInfo> &inventorys);

    void onDownloadProgress(DUCType ducType, const DownloadProgress &progress);

    void Reset();

private:
    bool subscribeTopic();

    void onTimerCallback();

private:
    // 初始化状态
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_started{false};

    std::atomic<uint8_t> m_Progress = 0;        //进度
    std::atomic<uint64_t> m_downloadedSize = 0; //已下载大小
    std::atomic<uint64_t> m_totalSize = 0;      //总大小

    std::shared_ptr<Timer> m_timer;
    DownloadOverallProgressCallback m_DownloadOverallProgressCallback;
    mutable std::mutex m_mutex;
};

} // namespace fotamaster
} // namespace seres