#pragma once

#include "OTA_DucData.hpp"
#include "base/noncopyable.h"
#include "base/singleton.h"
#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/timer.h"
#include "logger/logger.h"

#include <atomic>
#include <chrono>
#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{

using namespace seres::ota_duc_service;
// 每个设备的进度信息
struct EachUpdateProgressInfo
{
    std::string DeviceId;        //id
    uint8_t currentProgress = 0; //进度
};
// 进度回调函数类型
using UpgradeOverallProgressCallback =
    std::function<void(uint8_t progress, bool allFinished, std::vector<EachUpdateProgressInfo> eachProgress)>;


class UpgradeProgressManager : public base::Noncopyable
{
public:
    // 单个设备进度信息
    struct PartProgressInfo
    {
        std::string partId;          //id
        uint8_t currentProgress = 0; //进度
        float weight;                //设备权重
    };
    // 域控整体进度信息
    struct DomainProgressInfo
    {
        std::vector<PartProgressInfo> devices; // 设备映射
        uint8_t domainProgress;                // 域控总体进度
        float weight;                          // 域控权重
        bool allFinished;                      // 是否全部完成
    };

    UpgradeProgressManager() = default;
    ~UpgradeProgressManager();
    bool Initialize(UpgradeOverallProgressCallback progressCallback, double reportInterval = 1.0, bool needSub = false);

    bool AddUpdateDevice(DUCType ducType, const std::vector<std::string> &inventorys);

    bool Start();

    void Stop();

    void Reset();

    uint8_t GetCurrentProgress() const;

    bool IsAllFinished() const;

    void SetSmoothingFactor(double smoothingFactor);

    void onUpdateProgress(DUCType ducType, const UpdateProgress &progress);

private:
    void onTimerCallback();

    bool subscribeTopic();

    std::vector<EachUpdateProgressInfo> getEachUpdateDevices();

    uint8_t calculateOverallProgress();

    uint8_t applySmoothingAlgorithm(uint8_t newProgress);

    void setDeviceWeight(const std::string &deviceName, double weight)
    {
    }

private:
    // 初始化状态
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_started{false};

    // 设备和域控进度信息
    std::unordered_map<DUCType, DomainProgressInfo> m_domainProgress;

    // 进度计算相关
    std::atomic<uint8_t> m_currentProgress{0};
    std::atomic<uint8_t> m_smoothedProgress{0};
    std::atomic<bool> m_allFinished{false};

    // 平滑算法参数
    double m_smoothingFactor = 0.5; // 平滑因子，越小越平滑

    // 回调和定时器
    UpgradeOverallProgressCallback m_UpgradeOverallProgressCallback;
    std::shared_ptr<Timer> m_timer;
    double m_reportInterval = 1.0;

    mutable std::mutex m_mutex;
};

} // namespace fotamaster
} // namespace seres