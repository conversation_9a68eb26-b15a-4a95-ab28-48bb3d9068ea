#pragma once

#include "base/noncopyable.h"
#include "dds_service_manager/duc_service_manager.h"
#include "progress_fitting/download_progress_fitting.h"
#include <cstdint>
#include <functional>

namespace seres
{
namespace fotamaster
{

class DUCServiceManager;

class DownloadManager : base::Noncopyable
{
public:
    using ProgressCallback = std::function<void(const uint32_t)>;
    using FailReasonCallback = std::function<void(const uint32_t)>;

    DownloadManager() = default;
    explicit DownloadManager(DUCServiceManager *duc_service_manager);

    void RegisterProgressCallback(ProgressCallback &&callback)
    {
        progress_callback_ = std::move(callback);
    }

    void RegisterFailReasonCallback(FailReasonCallback &&callback)
    {
        fail_reason_callback_ = std::move(callback);
    }

    // TODO 后续根据具体任务信息完善该函数参数
    bool CheckDownloadCondition(/* TODO add dowload check arg */);

    // TODO 后续根据具体任务信息完善该函数参数
    bool StartDownload(/* TODO add dowload packzge arg */);
    bool StopDownload();
    bool ResumeDownload();
    bool CancelDownload();

private:
    bool Initialize();
    void HandleDownloadProgress(DUCType type, const DownloadProgress &progress);
    bool HandleDownloadStageErr(DUCType type, const CommonStatus &result);
    uint32_t ReasonConvert(DUCType type, uint32_t reason);

private:
    DUCServiceManager *duc_srv_manager_{nullptr};
    DownloadProgressManager *progress_manager_;
    DownloadProgress download_progress_;
    ProgressCallback progress_callback_{nullptr};
    FailReasonCallback fail_reason_callback_{nullptr};
};

} // namespace fotamaster
} // namespace seres
