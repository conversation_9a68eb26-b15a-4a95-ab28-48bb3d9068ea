#pragma once
#include "base/noncopyable.h"
#include "dds_service_manager/vuc_service_adapter.hpp"
#include "state_machine/ota_state_machine.h"
#include "upgrade_manager.h"
#include <any>
#include <atomic>
#include <cstdint>
#include <memory>
#include <string>

namespace seres
{
namespace fotamaster
{

class EventLoop;
class OtaStateMachine;
class DUCServiceManager;
class InventoryManager;
class DownloadManager;

class FOTAMasterManager : base::Noncopyable
{
public:
    FOTAMasterManager() = default;
    explicit FOTAMasterManager(EventLoop *event_loop);
    ~FOTAMasterManager();

    bool TriggerInventoryCollection();

private:
    bool InitVucSubscriber();
    bool InitVucPublisher();
    bool InitDucServiceManager();
    void InitStateMachine();
    void InitInventoryManager();
    void InitDownloadManager();
    void InitUpgradeManager();

    bool EventHandleCb(const StateEvent &state, const std::any &data);
    bool HandleStartDownload();
    bool HandleStartUpgrade(UpgradeMode mode);
    bool HandleFault();

private:
    EventLoop *event_loop_{nullptr};
    std::unique_ptr<OtaStateMachine> ota_state_machine_{nullptr};
    std::shared_ptr<DUCServiceManager> duc_service_manager_{nullptr};
    std::shared_ptr<dds_wrapper::VucSubscriber> vuc_service_sub_{nullptr};
    std::shared_ptr<dds_wrapper::VucPublisher> vuc_service_pub_{nullptr};
    std::unique_ptr<InventoryManager> inventory_manager_{nullptr};
    std::unique_ptr<DownloadManager> download_manager_{nullptr};
    std::unique_ptr<UpgradeManager> upgrade_manager_{nullptr};
    std::atomic_bool support_seamless_upgrade_{false};
};

} // namespace fotamaster
} // namespace seres
