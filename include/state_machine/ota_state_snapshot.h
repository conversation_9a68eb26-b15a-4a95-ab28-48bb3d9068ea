#pragma once

#include "base/noncopyable.h"
#include "state_machine/ota_state_msg.h"
#include <string>
#include <cstdint>
#include <optional>

namespace seres
{
namespace fotamaster
{

/// @brief OTA升级状态快照类，用于存储管理未下载，下载未完成、未安装的等状态
class OtaStateSnapshot : base::Noncopyable
{
public:
    explicit OtaStateSnapshot(const std::string &history_path = std::string());
    // 创建快照
    bool TakeSnapshot(const OtaStateSnapshotInfo &snapshot_info);

    std::optional<OtaStateSnapshotInfo> GetSnapshot();

private:
    std::string history_path_{""};
};

}
}