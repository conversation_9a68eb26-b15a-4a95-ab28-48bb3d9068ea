#pragma once
#include "base/overload.h"
#include "nlohmann/json.hpp"
#include <cstdint>
#include <string>
#include <unordered_map>
#include <variant>

namespace seres
{
namespace fotamaster
{

using OrderedJson = nlohmann::ordered_json; // ordered_json 不自动排序

struct OtaTaskInfo
{
    std::string upgrade_info;
    std::string package_info;
};

enum class OtaInState : uint8_t
{
    kOtaInIdleState,
    kOtaInDownloadState,
    kOtaInSeamlessUpgradeState,
    kOtaInSeamlessUpgradeFinishState,
    kOtaInPreFormalUpgradeState,
    kOtaInFormalUpgradeState,
    kOtaInFaultState,
    kOtaInRollbackState,
    kOtaInExitState,
    kOtaInNR = 0xFF
};

using StateParamType = std::variant<OtaTaskInfo, uint32_t>;

struct OtaStateMsg
{
    OtaInState in_state;
    StateParamType state_param;
};

struct OtaStateSnapshotInfo
{
    std::string time;
    OtaStateMsg state_info;
};

const std::string kOtaStateSnapshotKey{"ota_state_snapshot"};

// JSON serialization for OtaTaskInfo
inline void to_json(OrderedJson &j, const OtaTaskInfo &o)
{
    j = OrderedJson{{"upgrade_info", o.upgrade_info},
                    {"package_info", o.package_info}};
}

inline void from_json(const OrderedJson &j, OtaTaskInfo &o)
{
    j.at("upgrade_info").get_to(o.upgrade_info);
    j.at("package_info").get_to(o.package_info);
}

// 定义 OtaInState 到字符串的映射
const std::unordered_map<OtaInState, std::string> g_ota_state_to_str = {
    {OtaInState::kOtaInIdleState, "kOtaInIdleState"},
    {OtaInState::kOtaInDownloadState, "kOtaInDownloadState"},
    {OtaInState::kOtaInSeamlessUpgradeState, "kOtaInSeamlessUpgradeState"},
    {OtaInState::kOtaInSeamlessUpgradeFinishState,
     "kOtaInSeamlessUpgradeFinishState"},
    {OtaInState::kOtaInPreFormalUpgradeState, "kOtaInPreFormalUpgradeState"},
    {OtaInState::kOtaInFormalUpgradeState, "kOtaInFormalUpgradeState"},
    {OtaInState::kOtaInFaultState, "kOtaInFaultState"},
    {OtaInState::kOtaInRollbackState, "kOtaInRollbackState"},
    {OtaInState::kOtaInExitState, "kOtaInExitState"},
    {OtaInState::kOtaInNR, "kOtaInNR"}};

// 定义 字符串到 OtaInState 的反向映射
const std::unordered_map<std::string, OtaInState> g_str_to_ota_state = [] {
    std::unordered_map<std::string, OtaInState> map;
    for (const auto &[state, str] : g_ota_state_to_str)
    {
        map[str] = state;
    }
    return map;
}();

// JSON serialization for OtaInState
inline void to_json(OrderedJson &j, const OtaInState &state)
{
    // 检查是否在映射表中，若不在则默认为 kOtaInNR
    auto it = g_ota_state_to_str.find(state);
    if (it != g_ota_state_to_str.end())
    {
        j = it->second;
    }
    else
    {
        j = "kOtaInNR"; // 默认值
    }
}

inline void from_json(const OrderedJson &j, OtaInState &state)
{
    // 获取 JSON 字符串
    auto s = j.get<std::string>();

    // 检查是否在反向映射表中，若不在则默认为 kOtaInNR
    auto it = g_str_to_ota_state.find(s);
    if (it != g_str_to_ota_state.end())
    {
        state = it->second;
    }
    else
    {
        state = OtaInState::kOtaInNR; // 默认值
    }
}

// JSON serialization for StateParamType (variant)
inline void to_json(OrderedJson &j, const StateParamType &param)
{
    std::visit(base::Overload{[&j](const OtaTaskInfo &task) {
                                  j = OrderedJson{{"OtaTaskInfo", task}};
                              },
                              [&j](uint32_t reason) {
                                  j = OrderedJson{{"Reason", reason}};
                              }},
               param);
}

inline void from_json(const OrderedJson &j, StateParamType &param)
{
    if (j.contains("OtaTaskInfo"))
    {
        param = j["OtaTaskInfo"].get<OtaTaskInfo>();
    }
    else if (j.contains("Reason"))
    {
        param = j["Reason"].get<uint32_t>();
    }
    else
    {
    }
}

// JSON serialization for OtaStateMsg
inline void to_json(OrderedJson &j, const OtaStateMsg &msg)
{
    j = OrderedJson{{"in_state", msg.in_state},
                    {"state_param", msg.state_param}};
}

inline void from_json(const OrderedJson &j, OtaStateMsg &msg)
{
    j.at("in_state").get_to(msg.in_state);
    j.at("state_param").get_to(msg.state_param);
}

// JSON serialization for OtaStateSnapshotInfo
inline void to_json(OrderedJson &j, const OtaStateSnapshotInfo &info)
{
    j = OrderedJson{{"time", info.time}, {"state_info", info.state_info}};
}

inline void from_json(const OrderedJson &j, OtaStateSnapshotInfo &info)
{
    j.at("time").get_to(info.time);
    j.at("state_info").get_to(info.state_info);
}

} // namespace fotamaster
} // namespace seres