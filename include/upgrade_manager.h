#pragma once

#include "OTA_DucData.hpp"
#include "base/noncopyable.h"
#include "progress_fitting/upgrade_progress_fitting.h"
#include <cstdint>
#include <functional>

namespace seres
{
namespace fotamaster
{

enum class UpgradeMode : uint8_t
{
    kSeamlessUpgrade,
    kFormalUpgrade
};

class DUCServiceManager;

class UpgradeManager : base::Noncopyable
{
public:
    using ProgressCallback = std::function<void(uint8_t)>;
    using FailReasonCallback = std::function<void(const uint32_t)>;

    UpgradeManager() = default;
    explicit UpgradeManager(DUCServiceManager *duc_service_manager);

    bool CheckUpgradeCondition();

    bool StartUpgrade(const UpgradeMode &upgrade_mode);
    bool CancelUpgrade();

    void RegisterProgressCallback(ProgressCallback &&callback)
    {
        progress_callback_ = std::move(callback);
    }

    void RegisterFailReasonCallback(FailReasonCallback &&callback)
    {
        fail_reason_callback_ = std::move(callback);
    }

private:
    bool Initialize();
    void HandleUpgradeProgress(DUCType type, const UpdateProgress &progress);
    bool HandleUpgradeStageErr(DUCType type, const CommonStatus &result);
    uint32_t ReasonConvert(DUCType type, uint32_t reason);

private:
    UpgradeProgressManager *progress_manager_{nullptr};
    DUCServiceManager *duc_srv_manager_{nullptr};
    ProgressCallback progress_callback_{nullptr};
    FailReasonCallback fail_reason_callback_{nullptr};
};

} // namespace fotamaster
} // namespace seres