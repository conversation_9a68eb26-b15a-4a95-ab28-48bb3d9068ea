#pragma once

#include "dds/dds.hpp"
#include "logger/logger.h"
#include <atomic>
#include <condition_variable>
#include <functional>
#include <future>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <thread>
#include <unordered_map>

namespace seres
{
namespace fotamaster
{

template <typename T>
class TopicSubscriber : public dds::sub::NoOpDataReaderListener<T>
{
public:
    using Callback = std::function<void(const T &)>;

    TopicSubscriber(dds::domain::DomainParticipant &participant,
                    const std::string &topicName,
                    const Callback &callback,
                    std::shared_ptr<dds::sub::Subscriber> subscriber = nullptr,
                    dds::sub::qos::DataReaderQos reader_qos =
                        dds::sub::qos::DataReaderQos())
        : m_callback(callback), m_topic_name(topicName)
    {
        try
        {
            // 创建话题
            dds::topic::Topic<T> topic(participant, topicName);
            if (subscriber)
            {
                m_subscriber = subscriber;
            }
            else
            {
                m_subscriber = std::make_shared<dds::sub::Subscriber>(
                    participant,
                    dds::sub::qos::SubscriberQos());
            }
            // 创建DataReader

            m_reader = std::make_shared<dds::sub::DataReader<T>>(*m_subscriber,
                                                                 topic,
                                                                 reader_qos);

            // 创建监听器并设置回调
            m_reader->listener(this, dds::core::status::StatusMask::all());

            LOG_INFO("Subscribe topic success, Topic_Name: %s",
                     topicName.c_str());
        }
        catch (const dds::core::Exception &e)
        {
            LOG_ERROR("Subscribe topic failed: %s", e.what());
        }
    }

    ~TopicSubscriber()
    {
        m_reader.reset();
    }

private:
    // 当有新数据可用时触发
    void on_data_available(dds::sub::DataReader<T> &reader) override
    {
        if (!m_callback)
        {
            LOG_ERROR("Callback is not set");
            return;
        }

        auto samples = reader.take();
        for (const auto &sample : samples)
        {
            if (sample.info().valid())
            {
                m_callback(sample.data());
            }
        }
    }

    // 当请求的截止时间错过时触发
    void on_requested_deadline_missed(
        dds::sub::DataReader<T> &reader,
        const dds::core::status::RequestedDeadlineMissedStatus &status) override
    {
        LOG_ERROR("[on_requested_deadline_missed] Topic_Name: %s, Total count: "
                  "%u, Total "
                  "change: %u\n",
                  m_topic_name.c_str(),
                  status.total_count(),
                  status.total_count_change());
    }

    // 当请求的QoS不兼容时触发
    void on_requested_incompatible_qos(
        dds::sub::DataReader<T> &reader,
        const dds::core::status::RequestedIncompatibleQosStatus &status)
        override
    {
        LOG_ERROR("[on_requested_incompatible_qos] Topic_Name: %s, Total "
                  "count: %u, Total "
                  "change: %u\n",
                  m_topic_name.c_str(),
                  status.total_count(),
                  status.total_count_change());
    }

    // 当样本被拒绝时触发
    void on_sample_rejected(
        dds::sub::DataReader<T> &reader,
        const dds::core::status::SampleRejectedStatus &status) override
    {
        LOG_ERROR("[on_sample_rejected] Topic_Name: %s, Total count: %u, Total "
                  "change: %u\n",
                  m_topic_name.c_str(),
                  status.total_count(),
                  status.total_count_change());
    }

    // 当活跃性改变时触发
    void on_liveliness_changed(
        dds::sub::DataReader<T> &reader,
        const dds::core::status::LivelinessChangedStatus &status) override
    {
        LOG_WARN(
            "[on_liveliness_changed] Topic_Name: %s, Alive count: %u, Not "
            "alive count: %u\n",
            m_topic_name.c_str(),
            status.alive_count(),
            status
                .not_alive_count()); // 我也添加了not_alive_count信息，因为它在状态中可用
    }

    // 当订阅匹配状态改变时触发
    void on_subscription_matched(
        dds::sub::DataReader<T> &reader,
        const dds::core::status::SubscriptionMatchedStatus &status) override
    {
        LOG_INFO("[on_subscription_matched] Topic_Name: %s, Current count: "
                 "%u, Current count "
                 "change: %u\n",
                 m_topic_name.c_str(),
                 status.current_count(),
                 status.current_count_change());
    }

    // 当样本丢失时触发
    void on_sample_lost(
        dds::sub::DataReader<T> &reader,
        const dds::core::status::SampleLostStatus &status) override
    {
        LOG_ERROR("[on_sample_lost] Topic_Name: %s, Total count: %u, Total "
                  "change: %u\n",
                  m_topic_name.c_str(),
                  status.total_count(),
                  status.total_count_change());
    }

private:
    Callback m_callback;
    std::string m_topic_name;
    std::shared_ptr<dds::sub::Subscriber> m_subscriber;
    std::shared_ptr<dds::sub::DataReader<T>> m_reader;
};

} // namespace fotamaster
} // namespace seres