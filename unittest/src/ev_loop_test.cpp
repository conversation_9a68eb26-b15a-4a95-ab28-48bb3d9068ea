#define CATCH_CONFIG_MAIN // 该宏用于生成Catch2的main函数
#include "catch2/catch.hpp"

#include "ev_loop/eventloop_manager.h"
#include "ev_loop/timer.h"
#include "ev_loop/signal_manager.h"
#include <sstream>
#include <signal.h>
#include <unistd.h>
#include <chrono>
#include "base/singleton.h"

using namespace seres::fotamaster;

TEST_CASE("EventLoopManager", "[eventloop]")
{
    std::ostringstream ss;
    ss << std::this_thread::get_id();
    auto main_thread_id = ss.str();

    SECTION("Multi EventLoops")
    {

        auto evl1 = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
        auto evl2 = base::Singleton<EventLoopManager>::Instance().GetThreadLoop();

        REQUIRE(evl1->name() == main_thread_id);
        REQUIRE(evl2->name() == evl1->name());

        evl1->RunInLoopThread(
            [evl1]() { REQUIRE(evl1->loop_thread() == std::thread::id()); });

        REQUIRE(evl2->loop_thread() == std::thread::id());
        evl2->RunInRunningLoopThread([evl2]() {
            REQUIRE(evl2->loop_thread() == std::this_thread::get_id());
        });

        REQUIRE(evl2->GetJobsQueueSize() == 1);
        auto result = evl2->LoopOnce();
        REQUIRE(evl2->loop_thread() == std::thread::id());
        REQUIRE(evl2->GetJobsQueueSize() == 0);
        REQUIRE(result == 1);
    }

    SECTION("EventLoop LoopForever")
    {
        std::thread t{[]() {
            auto this_loop = base::Singleton<EventLoopManager>::Instance().GetThreadLoop();
            REQUIRE(this_loop->loop_thread() == std::thread::id());
            this_loop->LoopForever();
            REQUIRE(this_loop->loop_thread() == std::thread::id());
        }};

        auto loop = base::Singleton<EventLoopManager>::Instance().GetThreadLoop(t.get_id());
        loop->WaitUntilLoopRunning();
        int i = 0;
        loop->RunInRunningLoopThread([loop, i]() {
            REQUIRE(loop->loop_thread() == std::this_thread::get_id());
            REQUIRE(i == 0);
            // loop->TerminateLoop();
        });

        ++i;
        loop->RunInLoopThread([i]() {
            REQUIRE(i == 1);
            // loop->TerminateLoop();
        });


        ++i;
        loop->RunInLoopThread([loop, i]() {
            REQUIRE(i == 2);

            auto j = i + 2;
            loop->RunInRunningLoopThread([j]() { REQUIRE(j == 4); }, true);
        });

        ++i;
        loop->RunInRunningLoopThread([loop, i]() {
            REQUIRE(i == 3);
            auto j = i + 1;
            loop->RunInRunningLoopThread([j]() { REQUIRE(j == 4); });

            ++j;
            loop->RunInLoopThread([j]() { REQUIRE(j == 5); });
            loop->TerminateLoop();
        });

        t.join();
    }
}

TEST_CASE("Timer", "[timer]")
{
    SECTION("After")
    {
        auto loop = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();

        Timer timer{loop};

        auto start = std::chrono::system_clock::now();
        auto retval = timer.SetCallback([start]() {
            auto end = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end - start;
            auto dur = end - start;
            UNSCOPED_INFO("timeout: " << diff.count() << " s.");
            CHECK(dur.count() > 0);
            base::Singleton<EventLoopManager>::Instance().GetDefaultLoop()->TerminateLoop();
        });
        REQUIRE(!retval);

        retval = timer.After(1.0);
        REQUIRE(!retval);

        auto start_point = std::chrono::system_clock::to_time_t(start);
        UNSCOPED_INFO("start point: " << std::ctime(&start_point));
        loop->LoopForever();
    }
    SECTION("Every")
    {
        auto loop = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();

        Timer timer{loop};

        auto start = std::chrono::system_clock::now();
        auto retval = timer.SetCallback([start]() {
            auto end = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end - start;
            auto dur = end - start;
            UNSCOPED_INFO("timeout: " << diff.count() << " s.");
            CHECK(dur.count() > 0);
            // base::Singleton<EventLoopManager>::Instance().GetDefaultLoop()->TerminateLoop();
        });
        REQUIRE(!retval);

        retval = timer.Every(2.0);
        REQUIRE(!retval);

        auto start_point = std::chrono::system_clock::to_time_t(start);
        UNSCOPED_INFO("start point: " << std::ctime(&start_point));
        loop->LoopForever();
    }
}

TEST_CASE("Signal Manager", "[signal]")
{
    SECTION("Multi signal handler")
    {
        auto loop = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();

        // auto &sig_mgr = SignalManager::GetManager();

        base::Singleton<SignalManager>::Instance(loop).RegisterHandler(SIGUSR1, [](int signum) {
            REQUIRE(signum == SIGUSR1);
        });

        base::Singleton<SignalManager>::Instance(loop).RegisterHandler(SIGTERM, [](int signum) {
            REQUIRE(signum == SIGTERM);
            base::Singleton<EventLoopManager>::Instance().GetThreadLoop()->TerminateLoop();
        });

        kill(getpid(), SIGUSR1);
        kill(getpid(), SIGTERM);
        loop->LoopForever();
    }
}